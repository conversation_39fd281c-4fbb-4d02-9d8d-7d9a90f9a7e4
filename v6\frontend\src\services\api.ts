import axios from 'axios';
import type { User, Task, ApiResponse } from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Re-export types for convenience
export type { User, Task, ApiResponse } from '../types';

// API functions
export const apiService = {
  // Health check
  async healthCheck() {
    const response = await api.get<{ message: string; timestamp: string; version: string }>('/');
    return response.data;
  },

  // Users API
  users: {
    async getAll() {
      const response = await api.get<ApiResponse<User[]>>('/api/users');
      return response.data;
    },

    async getById(id: number) {
      const response = await api.get<ApiResponse<User>>(`/api/users/${id}`);
      return response.data;
    },

    async create(userData: { name: string; email: string }) {
      const response = await api.post<ApiResponse<User>>('/api/users', userData);
      return response.data;
    },

    async update(id: number, userData: Partial<{ name: string; email: string }>) {
      const response = await api.put<ApiResponse<User>>(`/api/users/${id}`, userData);
      return response.data;
    },

    async delete(id: number) {
      const response = await api.delete<ApiResponse<User>>(`/api/users/${id}`);
      return response.data;
    },
  },

  // Tasks API
  tasks: {
    async getAll(filters?: { userId?: number; completed?: boolean }) {
      const params = new URLSearchParams();
      if (filters?.userId) params.append('userId', filters.userId.toString());
      if (filters?.completed !== undefined) params.append('completed', filters.completed.toString());
      
      const response = await api.get<ApiResponse<Task[]>>(`/api/tasks?${params}`);
      return response.data;
    },

    async getById(id: number) {
      const response = await api.get<ApiResponse<Task>>(`/api/tasks/${id}`);
      return response.data;
    },

    async create(taskData: { title: string; description?: string; userId: number }) {
      const response = await api.post<ApiResponse<Task>>('/api/tasks', taskData);
      return response.data;
    },

    async update(id: number, taskData: Partial<{ title: string; description: string; completed: boolean }>) {
      const response = await api.put<ApiResponse<Task>>(`/api/tasks/${id}`, taskData);
      return response.data;
    },

    async delete(id: number) {
      const response = await api.delete<ApiResponse<Task>>(`/api/tasks/${id}`);
      return response.data;
    },
  },
};

export default api;
