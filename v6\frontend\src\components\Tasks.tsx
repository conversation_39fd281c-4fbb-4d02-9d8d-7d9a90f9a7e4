import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import type { Task, User } from '../types';

const Tasks: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newTask, setNewTask] = useState({ title: '', description: '', userId: 1 });
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [filter, setFilter] = useState<{ userId?: number; completed?: boolean }>({});

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchTasks();
  }, [filter]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [tasksResponse, usersResponse] = await Promise.all([
        apiService.tasks.getAll(),
        apiService.users.getAll(),
      ]);
      setTasks(tasksResponse.data);
      setUsers(usersResponse.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchTasks = async () => {
    try {
      const response = await apiService.tasks.getAll(filter);
      setTasks(response.data);
    } catch (err) {
      setError('Failed to fetch tasks');
      console.error(err);
    }
  };

  const handleCreateTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTask.title) return;

    try {
      await apiService.tasks.create(newTask);
      setNewTask({ title: '', description: '', userId: 1 });
      fetchTasks();
    } catch (err) {
      setError('Failed to create task');
      console.error(err);
    }
  };

  const handleUpdateTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTask) return;

    try {
      await apiService.tasks.update(editingTask.id, {
        title: editingTask.title,
        description: editingTask.description,
        completed: editingTask.completed,
      });
      setEditingTask(null);
      fetchTasks();
    } catch (err) {
      setError('Failed to update task');
      console.error(err);
    }
  };

  const handleToggleComplete = async (task: Task) => {
    try {
      await apiService.tasks.update(task.id, { completed: !task.completed });
      fetchTasks();
    } catch (err) {
      setError('Failed to update task');
      console.error(err);
    }
  };

  const handleDeleteTask = async (id: number) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      await apiService.tasks.delete(id);
      fetchTasks();
    } catch (err) {
      setError('Failed to delete task');
      console.error(err);
    }
  };

  const getUserName = (userId: number) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  if (loading) return <div className="loading">Loading tasks...</div>;

  return (
    <div className="tasks-container">
      <h2>Tasks Management</h2>
      
      {error && <div className="error">{error}</div>}

      {/* Filters */}
      <div className="filters">
        <select
          value={filter.userId || ''}
          onChange={(e) => setFilter({ ...filter, userId: e.target.value ? parseInt(e.target.value) : undefined })}
        >
          <option value="">All Users</option>
          {users.map(user => (
            <option key={user.id} value={user.id}>{user.name}</option>
          ))}
        </select>
        <select
          value={filter.completed === undefined ? '' : filter.completed.toString()}
          onChange={(e) => setFilter({ ...filter, completed: e.target.value === '' ? undefined : e.target.value === 'true' })}
        >
          <option value="">All Tasks</option>
          <option value="false">Incomplete</option>
          <option value="true">Completed</option>
        </select>
      </div>

      {/* Create Task Form */}
      <div className="form-section">
        <h3>Add New Task</h3>
        <form onSubmit={handleCreateTask} className="task-form">
          <input
            type="text"
            placeholder="Task Title"
            value={newTask.title}
            onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
            required
          />
          <textarea
            placeholder="Description (optional)"
            value={newTask.description}
            onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
          />
          <select
            value={newTask.userId}
            onChange={(e) => setNewTask({ ...newTask, userId: parseInt(e.target.value) })}
          >
            {users.map(user => (
              <option key={user.id} value={user.id}>{user.name}</option>
            ))}
          </select>
          <button type="submit">Add Task</button>
        </form>
      </div>

      {/* Edit Task Form */}
      {editingTask && (
        <div className="form-section">
          <h3>Edit Task</h3>
          <form onSubmit={handleUpdateTask} className="task-form">
            <input
              type="text"
              placeholder="Task Title"
              value={editingTask.title}
              onChange={(e) => setEditingTask({ ...editingTask, title: e.target.value })}
              required
            />
            <textarea
              placeholder="Description"
              value={editingTask.description}
              onChange={(e) => setEditingTask({ ...editingTask, description: e.target.value })}
            />
            <label>
              <input
                type="checkbox"
                checked={editingTask.completed}
                onChange={(e) => setEditingTask({ ...editingTask, completed: e.target.checked })}
              />
              Completed
            </label>
            <button type="submit">Update Task</button>
            <button type="button" onClick={() => setEditingTask(null)}>Cancel</button>
          </form>
        </div>
      )}

      {/* Tasks List */}
      <div className="tasks-list">
        <h3>Tasks ({tasks.length})</h3>
        {tasks.length === 0 ? (
          <p>No tasks found.</p>
        ) : (
          <div className="tasks-grid">
            {tasks.map((task) => (
              <div key={task.id} className={`task-card ${task.completed ? 'completed' : ''}`}>
                <h4>{task.title}</h4>
                <p>{task.description}</p>
                <p className="task-user">Assigned to: {getUserName(task.userId)}</p>
                <p className="task-date">Created: {new Date(task.createdAt).toLocaleDateString()}</p>
                <div className="task-actions">
                  <button onClick={() => handleToggleComplete(task)}>
                    {task.completed ? 'Mark Incomplete' : 'Mark Complete'}
                  </button>
                  <button onClick={() => setEditingTask(task)}>Edit</button>
                  <button onClick={() => handleDeleteTask(task.id)} className="delete-btn">
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Tasks;
